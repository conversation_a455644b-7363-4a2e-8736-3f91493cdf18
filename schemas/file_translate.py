
from typing import List, Union
from pydantic import BaseModel

class TextTranslate(BaseModel):
    text: Union[str, List[str]]
    src: str = "eng_Latn"
    dst: str = "zho_Hans"
    service: str = "nllb200" #如果是使用大模型，参数就是服务:llm:glm4:latest"
    llm_api: str = ""
    llm_api_key: str = ""
    llm_prompt: str = ""
    llm_model: str = ""
    thread: int = 2

class TranslateResponse(BaseModel):
    code: int = 0
    message: str = ""
    task_id: str = ""