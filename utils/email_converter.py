import os
import extract_msg
import email
from email import policy
from datetime import datetime
from typing import Dict, List, Optional, Union

class EmailConverter:
    def __init__(self, save_attachments_dir: str = "attachments"):
        self.save_attachments_dir = save_attachments_dir
        if not os.path.exists(save_attachments_dir):
            os.makedirs(save_attachments_dir)

    def parse_email(self, file_path: str) -> Dict:
        """解析各种格式的邮件文件

        Args:
            file_path: 邮件文件路径

        Returns:
            Dict: 包含邮件信息的字典
        """
        ext = os.path.splitext(file_path)[1].lower()
        
        if ext == '.msg':
            return self._parse_msg(file_path)
        elif ext == '.emlx':
            return self._parse_emlx(file_path)
        elif ext == '.oft':
            return self._parse_oft(file_path)
        else:
            raise ValueError(f"Unsupported file format: {ext}")

    def _parse_msg(self, file_path: str) -> Dict:
        """解析.msg格式邮件"""
        msg = extract_msg.Message(file_path)
        attachments = []
        
        # 保存附件
        for attachment in msg.attachments:
            filename = attachment.longFilename or attachment.shortFilename
            if filename:
                save_path = os.path.join(self.save_attachments_dir, filename)
                with open(save_path, 'wb') as f:
                    f.write(attachment.data)
                attachments.append(save_path)

        return {
            'subject': msg.subject,
            'sender': msg.sender,
            'to': msg.to,
            'cc': msg.cc,
            'bcc': msg.bcc,
            'date': msg.date,
            'body': msg.body,
            'html_body': msg.htmlBody,
            'attachments': attachments
        }

    def _parse_emlx(self, file_path: str) -> Dict:
        """解析.emlx格式邮件"""
        with open(file_path, 'rb') as f:
            email_content = f.read()
        
        msg = email.message_from_bytes(email_content, policy=policy.default)
        attachments = []

        # 处理附件
        for part in msg.walk():
            if part.get_content_maintype() == 'multipart':
                continue
            if part.get('Content-Disposition') is None:
                continue

            filename = part.get_filename()
            if filename:
                save_path = os.path.join(self.save_attachments_dir, filename)
                with open(save_path, 'wb') as f:
                    f.write(part.get_payload(decode=True))
                attachments.append(save_path)

        return {
            'subject': msg['subject'],
            'sender': msg['from'],
            'to': msg['to'],
            'cc': msg['cc'],
            'bcc': msg['bcc'],
            'date': msg['date'],
            'body': self._get_email_body(msg, 'plain'),
            'html_body': self._get_email_body(msg, 'html'),
            'attachments': attachments
        }

    def _parse_oft(self, file_path: str) -> Dict:
        """解析.oft格式邮件"""
        # .oft文件本质上是.msg文件的模板形式
        return self._parse_msg(file_path)

    def _get_email_body(self, msg: email.message.Message, content_type: str) -> Optional[str]:
        """获取邮件正文"""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == f'text/{content_type}':
                    return part.get_payload(decode=True).decode()
        elif msg.get_content_type() == f'text/{content_type}':
            return msg.get_payload(decode=True).decode()
        return None