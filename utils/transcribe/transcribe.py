from typing import List
import whisperx
import torch
import threading
from whisperx.diarize import DiarizationPipeline

device_glbal = "cuda" if torch.cuda.is_available() else "cpu"
whisperx_model_name = "./models/whisperx"
wav2vec2_model = "./models/wav2vec2"
class WhisperXModel:
    _instance = None
    _initialized = False
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:  # 双重检查锁定
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self.diarize_model = None
                    self.whisperx_model = None
                    self._initialized = True
    
    def load(self):
        with self._lock:
            if self.diarize_model is None:
                print("正在加载模型 diarize ...")
                self.diarize_model = DiarizationPipeline(device=device_glbal)
            if self.whisperx_model is None:
                print("正在加载模型 whisperx...")
                self.whisperx_model = whisperx.load_model(whisperx_model_name, device=device_glbal, compute_type='float16')
    @property
    def is_loaded(self):
        return self.whisperx_model is not None and self.diarize_model is not None

# 创建全局实例
model = WhisperXModel()

speaker_data = {
    "SPEAKER_00": "说话人 01",
    "SPEAKER_01": "说话人 02",
    "SPEAKER_02": "说话人 03",
    "SPEAKER_03": "说话人 04",
    "SPEAKER_04": "说话人 05",
    "SPEAKER_05": "说话人 06",
    "SPEAKER_06": "说话人 07",
    '未知': '未知'
}
class WhisperXTranslator:
    def __init__(
        self, 
        file: str,
        output: str,
        language: str = None,
        is_diarize:bool = False,
        wav2vec2: bool = False,
        ):
        self.file = file
        self.output = output
        self.language = language
        self.is_diarize = is_diarize
        self.wav2vec2 = wav2vec2
        if not model.is_loaded:
            model.load()
        self.whisperx_model = model.whisperx_model
        self.diarize_model = model.diarize_model
        self.device = device_glbal

    @staticmethod
    def format_time_srt(seconds):
        """
        将秒数转换为 SRT 时间格式 (HH:MM:SS,mmm)
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"

    def __call__(self):

        try:
            transcription = None
            # 检查文件类型，处理视频文件
            import os
            import tempfile
            import ffmpeg
            
            file_ext = os.path.splitext(self.file)[1].lower()
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
            
            audio_file = self.file
            temp_audio = None
            
            # 如果是视频文件，先提取音频
            if file_ext in video_extensions:
                print(f"检测到视频文件: {self.file}，正在提取音频...")
                temp_audio = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
                try:
                    # 使用ffmpeg-python提取音频
                    (ffmpeg
                        .input(self.file)
                        .output(temp_audio, acodec='pcm_s16le', ar=16000, ac=1)
                        .overwrite_output()
                        .run(quiet=True, capture_stdout=True, capture_stderr=True)
                    )
                    self.file = temp_audio
                    print(f"音频提取成功: {temp_audio}")
                except ffmpeg.Error as e:
                    print(f"音频提取失败: {e.stderr.decode() if e.stderr else str(e)}")
                    if os.path.exists(temp_audio):
                        os.unlink(temp_audio)
                    return {"error": f"无法从视频中提取音频: {e.stderr.decode() if e.stderr else str(e)}"}
            audio = whisperx.load_audio(self.file)
            print(f"正在转写音频文件: {self.file}...")
            if self.language is None:
                print("未指定语言，将自动检测语言...")
                transcription = self.whisperx_model.transcribe(audio, batch_size=8)
                self.language = transcription["language"]
                print(f"检测到的语言: {self.language}")
            else:
                transcription = self.whisperx_model.transcribe(audio, language=self.language, batch_size=8)

            if self.is_diarize:
                # 将说话人标签添加到转写结果中
                if hasattr(self, 'wav2vec2') and self.wav2vec2:
                    print("正在进行词级时间戳对齐...")
                    model_a, metadata = whisperx.load_align_model(language_code=self.language, device=self.device, model_dir=wav2vec2_model)
                    transcription = whisperx.align(transcription["segments"], model_a, metadata, audio, self.device, return_char_alignments=False)
                print("正在进行说话人分离...")
                diarize_segments = self.diarize_model(audio)
                # 将说话人标签添加到转写结果中
                print("正在添加说话人标签...")
                # transcription = whisperx.assign_speaker_labels(transcription, diarize_segments)
                transcription = whisperx.assign_word_speakers(diarize_segments, transcription)

            # 清理临时文件
            if temp_audio and os.path.exists(temp_audio):
                os.unlink(temp_audio) 
            return self.save_transcription(transcription)
        except Exception as e:
            # 确保清理临时文件
            if 'temp_audio' in locals() and temp_audio and os.path.exists(temp_audio):
                os.unlink(temp_audio)
            print(f"转写失败: {str(e)}")
            return {
                "error": str(e)
            }

    
    def save_transcription(self, transcription) -> List[dict]:
        result = []
        for segment in transcription["segments"]:
            start_time = segment.get("start")  # 使用 get 方法获取 start 属性，避免 KeyError
            end_time = segment.get("end")  # 使用 get 方法获取 end 属性，避免 KeyError
            text = segment.get("text", '')
            speaker = segment.get("speaker", '未知')
            speaker = speaker_data.get(speaker, speaker)
            result.append({
                "start_time": start_time,
                "end_time": end_time,
                "text": text,
                "speaker": speaker,
            })
        
        return result



# def transcribe(
    
# )