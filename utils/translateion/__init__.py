from pdf2zh_next import SettingsModel
from pdf2zh_next.translator import BaseRateLimiter
from pdf2zh_next.translator.base_translator import BaseTranslator

from utils.translateion.openai_translate import OpenaiTranslate
def new_translator(
        # service: str = '',
        # llm_api: str = '',
        # llm_api_key: str = '',
        # llm_model: str = '',
        # lang_in: str = '',
        # lang_out: str = '',
        # thread: int = 1,
        # ignore_cache: bool = False,
        # prompt: str = '',
        settings: SettingsModel,
        rate_limiter: BaseRateLimiter,
) -> BaseTranslator:
    translate = None
    service = settings.translate_engine_settings.translate_engine_type.strip()
    for translator in [OpenaiTranslate]:
        if service == translator.name:
            translate = translator(
                settings=settings,
                rate_limiter=rate_limiter,
            )
    if not translate:
        raise ValueError("Unsupported translation service")

    return translate


__all__ = [
    "new_translator",
    "BaseTranslator"
]