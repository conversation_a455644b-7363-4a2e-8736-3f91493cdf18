from typing import Union, List

import pycld2
from pdf2zh_next import SettingsModel
from pdf2zh_next.translator import BaseRateLimiter
from pdf2zh_next.translator.base_translator import BaseTranslator
from tenacity import retry
from tenacity import retry_if_exception_type
from tenacity import stop_after_attempt
from tenacity import wait_exponential
from tenacity import before_sleep_log
from core.log import setup_logger
logger = setup_logger(__name__)
import logging
import torch
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
import threading
# 添加全局锁
_translate_lock = threading.Lock()
# pycld2 语言代码到 NLLB 语言代码的映射
pycld2_to_nllb = {
    # 欧洲语系
    'en': 'eng_Latn',  # 英语
    'fr': 'fra_Latn',  # 法语
    'es': 'spa_Latn',  # 西班牙语
    'pt': 'por_Latn',  # 葡萄牙语
    'de': 'deu_Latn',  # 德语
    'it': 'ita_Latn',  # 意大利语
    'nl': 'nld_Latn',  # 荷兰语
    'pl': 'pol_Latn',  # 波兰语
    'ru': 'rus_Cyrl',  # 俄语
    'uk': 'ukr_Cyrl',  # 乌克兰语
    'bg': 'bul_Cyrl',  # 保加利亚语
    'cs': 'ces_Latn',  # 捷克语
    'da': 'dan_Latn',  # 丹麦语
    'el': 'ell_Grek',  # 希腊语
    'fi': 'fin_Latn',  # 芬兰语
    'hu': 'hun_Latn',  # 匈牙利语
    'ro': 'ron_Latn',  # 罗马尼亚语
    'sv': 'swe_Latn',  # 瑞典语

    # 亚洲语系
    'zh': 'zho_Hans',  # 简体中文
    'zh-Hant': 'zho_Hant',  # 繁体中文
    'ja': 'jpn_Jpan',  # 日语
    'ko': 'kor_Hang',  # 韩语
    'th': 'tha_Thai',  # 泰语
    'vi': 'vie_Latn',  # 越南语
    'id': 'ind_Latn',  # 印尼语
    'ms': 'msa_Latn',  # 马来语
    'hi': 'hin_Deva',  # 印地语
    'bn': 'ben_Beng',  # 孟加拉语
    'ta': 'tam_Taml',  # 泰米尔语
    'te': 'tel_Telu',  # 泰卢固语
    'ur': 'urd_Arab',  # 乌尔都语
    'fa': 'fas_Arab',  # 波斯语

    # 阿拉伯语系
    'ar': 'arb_Arab',  # 阿拉伯语

    # 非洲语系
    'sw': 'swh_Latn',  # 斯瓦希里语
    'am': 'amh_Ethi',  # 阿姆哈拉语
    'ha': 'hau_Latn',  # 豪萨语

    # 其他语系
    'he': 'heb_Hebr',  # 希伯来语
    'tr': 'tur_Latn',  # 土耳其语
    'ka': 'kat_Geor',  # 格鲁吉亚语
    'my': 'mya_Mymr',  # 缅甸语
    'km': 'khm_Khmr',  # 柬埔寨语
    'lo': 'lao_Laoo',  # 老挝语
    'si': 'sin_Sinh',  # 僧伽罗语
}

device_glbal = "cuda" if torch.cuda.is_available() else "cpu"
model_name = "./models/nllb-200-600m"


# 使用单例模式管理模型
class NLLBModel:
    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:  # 双重检查锁定
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self.tokenizer = None
                    self.model = None
                    self._initialized = True

    def load(self):
        with self._lock:
            if self.tokenizer is None:
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            if self.model is None:
                self.model = AutoModelForSeq2SeqLM.from_pretrained(model_name).to(device_glbal)

    @property
    def is_loaded(self):
        return self.model is not None and self.tokenizer is not None


# 创建全局实例
nllb = NLLBModel()
nllb.load()


class CudaMemoryManager:
    """CUDA 内存管理器，用于自动清理 CUDA 缓存"""

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

class Nllb200Translation(BaseTranslator):
    name = "nllb200"

    def __init__(
            self,
            settings: SettingsModel,
            rate_limiter: BaseRateLimiter,
            lang_in:str = "eng_Latn",
            lang_out:str = "zho_Hans",
    ):
        super().__init__(settings, rate_limiter)
        self.tokenizer = nllb.tokenizer
        self.model = nllb.model
        self.device = device_glbal

    def detect_language(self, text):
        """使用 pycld2 检测文本语言"""
        if not text or len(text.strip()) < 5:
            return "eng_Latn"  # 默认返回英语

        try:
            # 预处理文本
            processed_text = text.replace('\n', ' ').strip()
            # 获取预测结果
            # reliable: 检测结果是否可靠
            # text_bytes_found: 实际处理的文本字节数
            # details: 包含语言检测的详细信息，是一个元组列表

            reliable, _, details = pycld2.detect(processed_text, bestEffort=True)

            # details[0] 包含最可能的语言信息
            # 格式: (语言名称, 语言代码, 可信度百分比, 是否可靠)
            lang_code = details[0][1]
            confidence = details[0][2]

            logger.debug(f"pycld2 检测结果: 语言={lang_code}, 可信度={confidence}%, 可靠性={reliable}")

            # 转换为 NLLB 语言代码
            nllb_code = pycld2_to_nllb.get(lang_code, None)
            if nllb_code:
                logger.info(f"检测到语言: {lang_code} -> {nllb_code} (可信度: {confidence}%)")
                return nllb_code
            else:
                logger.warning(f"无法将 pycld2 语言代码 {lang_code} 映射到 NLLB 语言代码")
                return "eng_Latn"  # 默认返回英语

        except pycld2.error as e:
            logger.error(f"语言检测失败: {str(e)}")
            return "eng_Latn"  # 默认返回英语
    @retry(
        retry=retry_if_exception_type(Exception),
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=1, max=15),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )
    def do_translate(self, text: Union[str, List[str]], rate_limit_params: dict = None)-> Union[str, List[str]]:
        is_single = isinstance(text, str)

        # 将单个文本转换为列表以统一处理
        if is_single:
            texts = [text]
        else:
            texts = text

        valid_indices = []
        valid_texts = []
        for i, t in enumerate(texts):
            if t:
                valid_indices.append(i)
                valid_texts.append(t)

        if not valid_texts:
            return "" if is_single else []
        try:
            with _translate_lock:
                # 准备源语言和目标语言代码
                src_lang = self.lang_in
                # 当传入的是auto时，会自动检测语言
                if src_lang.lower() == "auto" and valid_texts:
                    # 使用第一个有效文本进行语言检测
                    src_lang = self.detect_language(valid_texts[0])
                    logger.info(f"自动检测语言结果: {src_lang}")

                self.tokenizer.src_lang = src_lang
                # 当传入的是auto时，会自动检测语言
                # 生成翻译
                batch_size = 8
                result = []
                for i in range(0, len(valid_texts), batch_size):
                    with CudaMemoryManager():
                        batch = valid_texts[i:i + batch_size]
                        # 批量编码输入文本
                        inputs = self.tokenizer(batch, return_tensors="pt", padding=True, truncation=True,
                                                max_length=512)
                        inputs = {k: v.to(self.device) for k, v in inputs.items()}
                        with torch.no_grad():
                            outputs = self.model.generate(
                                **inputs,
                                forced_bos_token_id=self.tokenizer.convert_tokens_to_ids(self.lang_out),
                                max_length=512,
                                num_beams=5,
                                early_stopping=True
                            )

                        # 解码翻译结果
                        translations = self.tokenizer.batch_decode(outputs, skip_special_tokens=True)
                        # print(f"翻译结果: {translations}")
                        result.extend(translations)
                    # 主动清理不再需要的张量
                    del inputs, outputs
                    # 清理 CUDA 缓存
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

                # 将结果放回原始位置
                results = [""] * len(texts) if not is_single else [""]
                for i, trans_idx in enumerate(valid_indices):
                    # 确保结果是有效的字符串
                    if result[i] is not None and isinstance(result[i], str):
                        results[trans_idx] = result[i]
                    else:
                        # 无效结果使用原文
                        results[trans_idx] = texts[trans_idx] if texts[trans_idx] else ""

                # 最后再次清理 CUDA 缓存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                res = results[0] if is_single else results
                return res

        except Exception as e:
            logger.error(f"NLLB翻译失败: {str(e)}")
            # 翻译失败时返回原文
            return text if is_single else texts