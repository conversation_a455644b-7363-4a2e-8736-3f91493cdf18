from .converter import Converter
from string import Template
from typing import Dict, Any
import os
from utils.office_translate import word_translate_v1 

def translate_pdf_to_docx(
    file: str,
    output: str,
    lang_in: str,
    lang_out: str,
    service: str,
    model: None,
    thread: int = 1,
    prompt: Template = None,
    envs: Dict = None,
    ignore_cache: bool = False,
    **kwargs: Any
):
    if not os.path.exists(file):
        raise Exception(f"找不到文件: {file}")

    print(f"file: {os.path.basename(file)}")
    print("ocr: ", envs.get("is_ocr", 0))
    
    
    try:
        to_docx_patn = os.path.join(output, os.path.basename(file)+".docx")
        converter = Converter(file)
        result = converter.convert(to_docx_patn, ocr=envs.get("is_ocr", 0), ocr_language=envs.get("ocr_lang", "eng"))
        if result != None:
            raise Exception(f"转换失败: {result}")

        if envs.get("is_translate", False) == False:
            return {
                "text": "",
                "file": to_docx_patn,
            }

        # tanslate_path = word_translate(
        #     file=to_docx_patn,
        #     output=output,
        #     lang_in=lang_in,
        #     lang_out=lang_out,
        #     service=service,
        #     model=model,
        #     thread=thread,
        #     prompt=prompt,
        #     envs=envs,
        #     ignore_cache=ignore_cache,
        # )
        results = word_translate_v1(
                file=to_docx_patn,
                output=output,
                service=service,
                llm_api=envs["OPENAI_BASE_URL"],
                llm_api_key=envs["OPENAI_API_KEY"],
                llm_model=envs["OPENAI_MODEL"],
                lang_in=lang_in,
                lang_out=lang_out,
                thread=thread,
                ignore_cache=ignore_cache,
                prompt=prompt,
            )
        os.remove(to_docx_patn)
        return results
    except Exception as e:
        raise Exception(f"转换失败: {e}")

