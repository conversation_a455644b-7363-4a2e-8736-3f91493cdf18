# -*- coding: utf-8 -*-

'''
A wrapper of PyMuPDF Page as page engine.
'''

import fitz
import logging
from .RawPage import RawPage
from ..image.ImagesExtractor import ImagesExtractor
from ..shape.Paths import Paths
from ..common.constants import FACTOR_A_HALF
from ..common.Element import Element
from ..common.share import (RectType, debug_plot)
from ..common.algorithm import get_area


class RawPageFitz(RawPage):
    '''A wrapper of ``fitz.Page`` to extract source contents.'''

    def extract_raw_dict(self, **settings):
        raw_dict = {}
        if not self.page_engine: return raw_dict

        # actual page size
        # `self.page_engine` is the `fitz.Page`.
        *_, w, h = self.page_engine.rect # always reflecting page rotation
        raw_dict.update({ 'width' : w, 'height': h })
        self.width, self.height = w, h

        # pre-processing layout elements. e.g. text, images and shapes
        text_blocks = self._preprocess_text(**settings)
        raw_dict['blocks'] = text_blocks

        image_blocks = self._preprocess_images(**settings)
        raw_dict['blocks'].extend(image_blocks)
        
        shapes, images =  self._preprocess_shapes(**settings)
        raw_dict['shapes'] = shapes
        raw_dict['blocks'].extend(images)

        hyperlinks = self._preprocess_hyperlinks()
        raw_dict['shapes'].extend(hyperlinks)        
       
        # Element is a base class processing coordinates, so set rotation matrix globally
        Element.set_rotation_matrix(self.page_engine.rotation_matrix)

        return raw_dict
    

    def _preprocess_text(self, **settings):
        '''Extract page text and identify hidden text. 
        
        NOTE: All the coordinates are relative to un-rotated page.

            https://pymupdf.readthedocs.io/en/latest/page.html#modifying-pages
            https://pymupdf.readthedocs.io/en/latest/functions.html#Page.get_texttrace
            https://pymupdf.readthedocs.io/en/latest/textpage.html
        '''
        ocr = settings['ocr']
        # if ocr==1: raise SystemExit("OCR feature is planned but not implemented yet.")
        # 实现OCR功能
        if ocr==1:
            # 检查页面是否包含文本
            text = self.page_engine.get_text("text")
            if len(text.strip()) < 150:
                # 如果页面不包含文本，进行OCR处理
                try:
                    import pytesseract
                    from PIL import Image
                    import numpy as np
                    
                    # OCR语言设置，可以从settings中获取
                    lang = settings.get('ocr_language', 'chi_sim+eng')
                    
                    # 将页面渲染为图像
                    pix = self.page_engine.get_pixmap(alpha=False)
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    
                    # 使用Tesseract进行OCR
                    ocr_text = pytesseract.image_to_string(img, lang=lang)
                    
                    # 将OCR结果转换为文本块格式
                    if ocr_text.strip():
                        # 创建一个包含OCR文本的块
                        ocr_blocks = self._create_ocr_blocks(ocr_text, pix.width, pix.height)
                        return ocr_blocks
                except ImportError:
                    logging.error("pytesseract not installed. Please install it with 'pip install pytesseract' and ensure Tesseract OCR is installed on your system.")
                    raise SystemExit("OCR requires pytesseract and Tesseract OCR to be installed.")
                except Exception as e:
                    logging.error(f"OCR processing failed: {str(e)}")
                    # 如果OCR失败，继续使用原始文本提取方法

        # all text blocks no matter hidden or not
        sort = settings.get('sort')
        raw = self.page_engine.get_text(
                'rawdict',
                flags=0
                    | fitz.TEXT_MEDIABOX_CLIP
                    | fitz.TEXT_CID_FOR_UNKNOWN_UNICODE
                    ,
                sort=sort,
                )
        text_blocks = raw.get('blocks', [])

        # potential UnicodeDecodeError issue when trying to filter hidden text:
        # https://github.com/dothinking/pdf2docx/issues/144
        # https://github.com/dothinking/pdf2docx/issues/155
        try:
            spans = self.page_engine.get_texttrace()
        except SystemError:
            logging.warning('Ignore hidden text checking due to UnicodeDecodeError in upstream library.')
            spans = []
        
        if not spans: return text_blocks

        # ignore hidden text if ocr=0, while extract only hidden text if ocr=2
        if ocr==2:
            f = lambda span: span['type']!=3  # find displayed text and ignore it
        else:
            f = lambda span: span['type']==3  # find hidden text and ignore it
        filtered_spans = list(filter(f, spans))
        
        def span_area(bbox):
            x0, y0, x1, y1 = bbox
            return (x1-x0) * (y1-y0)

        # filter blocks by checking span intersection: mark the entire block if 
        # any span is matched
        blocks = []
        for block in text_blocks:
            intersected = False
            for line in block['lines']:
                for span in line['spans']:
                    for filter_span in filtered_spans:
                        intersected_area = get_area(span['bbox'], filter_span['bbox'])
                        if intersected_area / span_area(span['bbox']) >= FACTOR_A_HALF \
                            and span['font']==filter_span['font']:
                            intersected = True
                            break
                    if intersected: break # skip further span check if found
                if intersected: break     # skip further line check

            # keep block if no any intersection with filtered span
            if not intersected: blocks.append(block)

        return blocks

    def _create_ocr_blocks(self, ocr_text, width, height):
        '''创建包含OCR文本的块结构
        
        Args:
            ocr_text (str): OCR识别的文本
            width (float): 页面宽度
            height (float): 页面高度
            
        Returns:
            list: 文本块列表
        '''
        # 将OCR文本分割为段落
        paragraphs = [p for p in ocr_text.split('\n\n') if p.strip()]
        
        blocks = []
        
        # 计算合理的行高和段落间距
        line_height = height / 50  # 根据页面高度动态计算行高
        paragraph_spacing = line_height * 1.5  # 段落间距为行高的1.5倍
        
        y_pos = height * 0.05  # 从页面顶部5%的位置开始
        
        for i, paragraph in enumerate(paragraphs):
            lines = paragraph.split('\n')
            block_lines = []
            
            for j, line_text in enumerate(lines):
                if not line_text.strip():
                    continue
                    
                # 使用页面高度计算行的位置
                y0 = y_pos + j * line_height
                y1 = y0 + line_height
                
                # 确保不超出页面边界
                if y1 > height * 0.95:  # 保留页面底部5%的边距
                    break
                
                # 创建span
                span = {
                    'bbox': [width * 0.05, y0, width * 0.95, y1],  # 使用页面宽度的5%-95%
                    'font': 'ocr_font',
                    'size': line_height * 0.7,  # 字体大小为行高的70%
                    'flags': 0,
                    'color': 0,
                    'text': line_text,
                    'origin': [width * 0.05, y1]  # 文本基线位置
                }
                
                # 创建line
                line = {
                    'bbox': [width * 0.05, y0, width * 0.95, y1],
                    'wmode': 0,
                    'dir': [1, 0],
                    'spans': [span]
                }
                
                block_lines.append(line)
            
            # 创建block
            if block_lines:
                block = {
                    'type': 0,  # 文本类型
                    'bbox': [width * 0.05, block_lines[0]['bbox'][1], width * 0.95, block_lines[-1]['bbox'][3]],
                    'lines': block_lines
                }
                blocks.append(block)
                
                # 更新下一个段落的起始位置
                y_pos = block_lines[-1]['bbox'][3] + paragraph_spacing
        
        return blocks

    def _preprocess_images(self, **settings):
        '''Extract image blocks. Image block extracted by ``page.get_text('rawdict')`` doesn't 
        contain alpha channel data, so it has to get page images by ``page.get_images()`` and 
        then recover them. Note that ``Page.get_images()`` contains each image only once, i.e., 
        ignore duplicated occurrences.
        '''
        # ignore image if ocr-ed pdf: get ocr-ed text only
        ocr = settings['ocr']
        if ocr == 1 or ocr == 2: return []
        
        return ImagesExtractor(self.page_engine).extract_images(settings['clip_image_res_ratio'])


    def _preprocess_shapes(self, **settings):
        '''Identify iso-oriented paths and convert vector graphic paths to pixmap.'''
        paths = self._init_paths(**settings)
        return paths.to_shapes_and_images(
            settings['min_svg_gap_dx'], 
            settings['min_svg_gap_dy'], 
            settings['min_svg_w'], 
            settings['min_svg_h'], 
            settings['clip_image_res_ratio'])
    

    @debug_plot('Source Paths')
    def _init_paths(self, **settings):
        '''Initialize Paths based on drawings extracted with PyMuPDF.'''
        raw_paths = self.page_engine.get_cdrawings()
        return Paths(parent=self).restore(raw_paths)
    

    def _preprocess_hyperlinks(self):
        """Get source hyperlink dicts.

        Returns:
            list: A list of source hyperlink dict.
        """
        hyperlinks = []
        for link in self.page_engine.get_links():
            if link['kind']!=2: continue # consider internet address only
            hyperlinks.append({
                'type': RectType.HYPERLINK.value,
                'bbox': tuple(link['from']),
                'uri' : link['uri']
            })

        return hyperlinks
