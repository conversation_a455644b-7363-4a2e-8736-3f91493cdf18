
import threading
import time
import unicodedata
from abc import ABC
from abc import abstractmethod
from utils.babeldoc.document_il.translator.cache import TranslationCache
import logging
from typing import Callable, List, Union
import re
logger = logging.getLogger(__name__)

def remove_control_characters(s):
    return "".join(ch for ch in s if unicodedata.category(ch)[0] != "C")


class RateLimiter:
    def __init__(self, max_qps: int):
        self.max_qps = max_qps
        self.min_interval = 1.0 / max_qps
        self.last_requests = []  # Track last N requests
        self.window_size = max_qps  # Track requests in a sliding window
        self.lock = threading.Lock()

    def wait(self):
        with self.lock:
            now = time.time()

            # Clean up old requests outside the 1-second window
            while self.last_requests and now - self.last_requests[0] > 1.0:
                self.last_requests.pop(0)

            # If we have less than max_qps requests in the last second, allow immediately
            if len(self.last_requests) < self.max_qps:
                self.last_requests.append(now)
                return

            # Otherwise, wait until we can make the next request
            next_time = self.last_requests[0] + 1.0
            if next_time > now:
                time.sleep(next_time - now)
            self.last_requests.pop(0)
            self.last_requests.append(next_time)

    def set_max_qps(self, max_qps):
        self.max_qps = max_qps
        self.min_interval = 1.0 / max_qps
        self.window_size = max_qps


_translate_rate_limiter = RateLimiter(5)


def set_translate_rate_limiter(max_qps):
    _translate_rate_limiter.set_max_qps(max_qps)

def remove_control_characters(s):
    return "".join(ch for ch in s if unicodedata.category(ch)[0] != "C")

class TextSegment:
    """文本片段，用于区分可翻译和不可翻译的内容"""
    def __init__(self, text: str, translatable: bool):
        self.text = text
        self.translatable = translatable


class BaseTranslator(ABC):
    # Due to cache limitations, name should be within 20 characters.
    # cache.py: translate_engine = CharField(max_length=20)
    name = "base"
    lang_map = {}

    def __init__(self, lang_in, lang_out, ignore_cache):
        self.ignore_cache = ignore_cache
        lang_in = self.lang_map.get(lang_in.lower(), lang_in)
        lang_out = self.lang_map.get(lang_out.lower(), lang_out)
        self.lang_in = lang_in
        self.lang_out = lang_out

        self.cache = TranslationCache(
            self.name,
            {
                "lang_in": lang_in,
                "lang_out": lang_out,
            },
        )

        self.translate_call_count = 0
        self.translate_cache_call_count = 0

    # def __del__(self):
        # with contextlib.suppress(Exception):
        #     logger.info(
        #         f"{self.name} translate call count: {self.translate_call_count}"
        #     )
        #     logger.info(
        #         f"{self.name} translate cache call count: {self.translate_cache_call_count}",
        #     )

    def add_cache_impact_parameters(self, k: str, v):
        """
        Add parameters that affect the translation quality to distinguish the translation effects under different parameters.
        :param k: key
        :param v: value
        """
        self.cache.add_params(k, v)

    def translate(self, text, ignore_cache=False, rate_limit_params: dict = None):
        return self._run_translate(text, ignore_cache, rate_limit_params, self.do_translate)

    def llm_translate(self, text, ignore_cache=False, rate_limit_params: dict = None):
        return self._run_translate(text, ignore_cache, rate_limit_params, self.do_llm_translate)
        

    def _split_text_into_segments(self, text: str) -> list[TextSegment]:
        """
        将文本分割为可翻译和不可翻译的片段
        :param text: 原始文本
        :return: 文本片段列表
        """
        if not text:
            return []
        
        segments = []
        current_pos = 0
        
        # 定义需要保护的特殊内容的正则表达式
        patterns = [
            # 表情符号
            re.compile(
                "["
                "\U0001F600-\U0001F64F"  # 表情符号
                "\U0001F300-\U0001F5FF"  # 符号和象形文字
                "\U0001F680-\U0001F6FF"  # 交通和地图符号
                "\U0001F700-\U0001F77F"  # 炼金术符号
                "\U0001F780-\U0001F7FF"  # 几何形状
                "\U0001F800-\U0001F8FF"  # 补充箭头
                "\U0001F900-\U0001F9FF"  # 补充符号和象形文字
                "\U0001FA00-\U0001FA6F"  # 象棋符号
                "\U0001FA70-\U0001FAFF"  # 符号和象形文字扩展
                "\U00002702-\U000027B0"  # 装饰符号
                # "\U000024C2-\U0001F251" # 中文
                "]+", flags=re.UNICODE),
            # 连续的特殊字符序列
            re.compile(r'([=*#~\-+]{3,}|\={10,}|\*{10,}|\#{10,}|\~{10,}|\-{10,}|\+{10,})'),
            # URL
            re.compile(r'(https?://\S+)'),
            # 电子邮件地址
            re.compile(r'([a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+)'),
            # Markdown链接
            re.compile(r'(\[.+?\]\(.+?\))'),
            # HTML标签
            re.compile(r'(<[^>]+>)'),
            # 代码块
            re.compile(r'(```[\s\S]*?```)'),
            # 数字序列（包括带小数点、逗号的数字）
            re.compile(r'(\d+([.,]\d+)*)'),
            # 特殊符号序列
            re.compile(r'([§®™©]+)'),
            # 单个特殊字符
            re.compile(r'([/‹›#]+)'),
            # 连续的标点符号
            re.compile(r'([\\/<>{}\[\]|_\-=+!@#$%^&*(),.?":;\']+)')
        ]
        
        # 查找所有需要保护的内容
        matches = []
        for pattern in patterns:
            for match in pattern.finditer(text):
                matches.append((match.start(), match.end(), match.group(0)))
        
        # 按照位置排序
        matches.sort(key=lambda x: x[0])
        
        # 合并重叠的匹配
        merged_matches = []
        if matches:
            current_start, current_end, current_text = matches[0]
            for start, end, match_text in matches[1:]:
                if start <= current_end:  # 有重叠
                    current_end = max(current_end, end)
                    current_text = text[current_start:current_end]
                else:  # 无重叠，添加当前匹配并开始新的匹配
                    merged_matches.append((current_start, current_end, current_text))
                    current_start, current_end, current_text = start, end, match_text
            merged_matches.append((current_start, current_end, current_text))
        
        # 构建分段
        for start, end, match_text in merged_matches:
            # 添加匹配前的可翻译文本
            if start > current_pos:
                translatable_text = text[current_pos:start]
                if translatable_text.strip():  # 只添加非空文本
                    segments.append(TextSegment(translatable_text, True))
            
            # 添加不可翻译的匹配文本
            segments.append(TextSegment(match_text, False))
            current_pos = end
        
        # 添加最后一个匹配后的可翻译文本
        if current_pos < len(text):
            translatable_text = text[current_pos:]
            if translatable_text.strip():  # 只添加非空文本
                segments.append(TextSegment(translatable_text, True))
        
        # 如果没有找到任何匹配，整个文本都是可翻译的
        if not segments:
            segments.append(TextSegment(text, True))
        
        return segments
    

    def _run_translate(self, text:Union[str, List[str]], ignore_cache=False, rate_limit_params: dict = None, tranFunc:Callable = None):
        """
        Translate the text, and the other part should call this method.
        :param text: text to translate
        :return: translated text
        """
        # if not (self.ignore_cache or ignore_cache):
        #     # cache = self.cache.get(text)
        #     if cache is not None:
        #         self.translate_cache_call_count += 1
        #         return cache
        _translate_rate_limiter.wait()
        translation = tranFunc(text, rate_limit_params)
        # if not (self.ignore_cache or ignore_cache):
        #     self.cache.set(text, translation)
        return translation

    @abstractmethod
    def do_llm_translate(self, text, rate_limit_params: dict = None):
        """
        Actual translate text, override this method
        :param text: text to translate
        :return: translated text
        """
        raise NotImplementedError

    @abstractmethod
    def do_translate(self, text, rate_limit_params: dict = None):
        """
        Actual translate text, override this method
        :param text: text to translate
        :return: translated text
        """
        logger.critical(
            f"Do not call BaseTranslator.do_translate. "
            f"Translator: {self}. "
            f"Text: {text}. ",
        )
        raise NotImplementedError

    def __str__(self):
        return f"{self.name} {self.lang_in} {self.lang_out} {self.model}"

    def get_rich_text_left_placeholder(self, placeholder_id: int):
        return f"<b{placeholder_id}>"

    def get_rich_text_right_placeholder(self, placeholder_id: int):
        return f"</b{placeholder_id}>"

    def get_formular_placeholder(self, placeholder_id: int):
        return self.get_rich_text_left_placeholder(placeholder_id)
