from .translateV1 import BaseTranslator

import httpx
import openai
from tenacity import retry
from tenacity import retry_if_exception_type
from tenacity import stop_after_attempt
from tenacity import wait_exponential
from utils.babeldoc.document_il.utils.atomic_integer import AtomicInteger
import re
import concurrent.futures
from typing import Union, List
from core.log import setup_logger

logger = setup_logger(__name__)



class OpenAITranslator(BaseTranslator):
    name = "openai"
    def __init__(
        self,
        service: str = '',
        llm_api: str = '',
        llm_api_key: str = '',
        llm_model: str = '',
        lang_in: str = '',
        lang_out: str = '',
        thread: int = 1,
        ignore_cache: bool = False,
        prompt: str = '',
    ):
        super().__init__(
            lang_in = lang_in, 
            lang_out = lang_out, 
            ignore_cache = ignore_cache,
        )
        self.thread = thread
        self.options = {"temperature": 0}  # 随机采样可能会打断公式标记
        self.client = openai.OpenAI(
            base_url=llm_api,
            api_key=llm_api_key,
            http_client=httpx.Client(
                limits=httpx.Limits(
                    max_connections=None, max_keepalive_connections=None
                )
            ),
        )
        self.add_cache_impact_parameters("temperature", self.options["temperature"])
        self.model = llm_model
        self.llm_prompt = prompt
        self.add_cache_impact_parameters("model", self.model)
        self.add_cache_impact_parameters("prompt", self.prompt(""))
        self.token_count = AtomicInteger()
        self.prompt_token_count = AtomicInteger()
        self.completion_token_count = AtomicInteger()

    @retry(
        retry=retry_if_exception_type(openai.RateLimitError),
        stop=stop_after_attempt(100),
        wait=wait_exponential(multiplier=1, min=1, max=15),
        before_sleep=lambda retry_state: logger.warning(
            f"RateLimitError, retrying in {retry_state.next_action.sleep} seconds... "
            f"(Attempt {retry_state.attempt_number}/100)"
        ),
    )
    def do_translate(self, text:Union[str, List[str]], rate_limit_params: dict = None) -> str:
        # print(f"do_translate: {text}")
        return self._llm_call(text, rate_limit_params)

    def prompt(self, text):
        return [
            {
                "role": "system",
                # "content": "You are a professional,authentic machine translation engine.",
                "content": self.llm_prompt if self.llm_prompt else "你是一位专业的、真实的机器翻译引擎。",
            },
            {
                "role": "user",
                # "content": f";; Treat next line as plain text input and translate it into {self.lang_out}, output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, {'{{1}}, etc. '}), return the original text. NO explanations. NO notes. Input:\n\n{text}",
                "content": f"请翻译以下内容：{text}" if self.llm_prompt else f";; 请将下一行视为纯文本输入，翻译成简体中文，输出翻译结果 ONLY。如果翻译是不必要的（例如专有名词、代码、{{1}} 等），返回原始文本。不要解释。不要备注。输入：\n\n{text}",
            },
        ]

    @retry(
        retry=retry_if_exception_type(openai.RateLimitError),
        stop=stop_after_attempt(100),
        wait=wait_exponential(multiplier=1, min=1, max=15),
        before_sleep=lambda retry_state: logger.warning(
            f"RateLimitError, retrying in {retry_state.next_action.sleep} seconds... "
            f"(Attempt {retry_state.attempt_number}/100)"
        ),
    )
    def do_llm_translate(self, text:Union[str, List[str]], rate_limit_params: dict = None):
        if text is None:
            return None
        # print(f"do_llm_translate: {text}")
        return self._llm_call(text, rate_limit_params, [
                {
                    "role": "user",
                    "content": text,
                },
            ])
        

    def _llm_call(self, text:Union[str, List[str]], rate_limit_params: dict = None, messages:List[dict] = None):
        # 定义单个文本的翻译函数
        def translate_single(t):
            if not t:
                return t
            try:
                current_messages = messages if messages else self.prompt(t)
                response = self.client.chat.completions.create(
                    model=self.model,
                    **self.options,
                    messages=current_messages,
                    extra_body={
                        "top_k": 20, 
                        "chat_template_kwargs": {"enable_thinking": False},
                    },
                )
                self.update_token_count(response)
                content = self._remove_cot_content(response.choices[0].message.content or "")
                
                return content 
            except Exception as e:
                logger.error(f"翻译失败: {str(e)}")
                return t  # 翻译失败时返回原文
        
        if isinstance(text, list):
            if not text:
                return []

            # 使用线程池并发执行翻译
            results = [None] * len(text)  # 预分配结果列表
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.thread) as executor:
                # 创建任务到索引的映射
                future_to_idx = {executor.submit(translate_single, t): i for i, t in enumerate(text)}
                
                # 收集结果，保持原始顺序
                for future in concurrent.futures.as_completed(future_to_idx):
                    idx = future_to_idx[future]
                    try:
                        results[idx] = future.result()
                    except Exception as e:
                        logger.error(f"处理翻译结果时出错: {str(e)}")
                        results[idx] = text[idx]  # 出错时保留原文
            
            return results

        elif isinstance(text, str):
            # if (max_token := len(text) * 5) > self.options["num_predict"]:
            #     self.options["num_predict"] = max_token
            return translate_single(text)
                
        
    @staticmethod
    def _remove_cot_content(content: str) -> str:
        """Remove text content with the thought chain from the chat response

        :param content: Non-streaming text content
        :return: Text without a thought chain
        """
        con = re.sub(r"^<think>.+?</think>", "", content, count=1, flags=re.DOTALL)
        con = re.sub(r"</think>", "", con, count=1, flags=re.DOTALL)
        return con

    def update_token_count(self, response):
        try:
            if response.usage and response.usage.total_tokens:
                self.token_count.inc(response.usage.total_tokens)
            if response.usage and response.usage.prompt_tokens:
                self.prompt_token_count.inc(response.usage.prompt_tokens)
            if response.usage and response.usage.completion_tokens:
                self.completion_token_count.inc(response.usage.completion_tokens)
        except Exception as e:
            logger.exception("Error updating token count")

    def get_formular_placeholder(self, placeholder_id: int):
        return "{v" + str(placeholder_id) + "}", f"{{\\s*v\\s*{placeholder_id}\\s*}}"

    def get_rich_text_left_placeholder(self, placeholder_id: int):
        return (
            f"<style id='{placeholder_id}'>",
            f"<\\s*style\\s*id\\s*=\\s*'\\s*{placeholder_id}\\s*'\\s*>",
        )

    def get_rich_text_right_placeholder(self, placeholder_id: int):
        return "</style>", r"<\s*\/\s*style\s*>"
