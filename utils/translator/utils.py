
import os
import time
import logging

log = logging.getLogger(__name__)

def is_numeric_or_currency(text):
    """检查文本是否为数字或货币格式"""
    if not isinstance(text, str):
        return True
    text = str(text).strip()
    # 移除货币符号和逗号
    text = text.replace('₱', '').replace(',', '').replace('P', '')
    try:
        float(text)
        return True
    except ValueError:
        return False


def get_output_path(file: str, output: str, prefix: str = "translated", file_ext: str = "") -> tuple[str, str]:
    """
    获取输出文件路径
    
    Args:
        file: 输入文件路径
        output: 输出目录
        prefix: 输出文件名前缀，默认为 "translated"
        file_ext: 文件后缀
    
    Returns:
        tuple[str, str]: (输出文件完整路径, 输出文件名)
    """
     # 创建输出目录（如果不存在）
    if not output:
        output_dir = os.path.dirname(file)
    elif os.path.isabs(output):
        output_dir = output
    else:
        output_dir = os.path.abspath(output)
    os.makedirs(output_dir, exist_ok=True)
    # 构建输出文件名（不包含output目录名）
    base_name = os.path.basename(file)
    output_filename = f"{prefix}_{base_name}"
    output_path = os.path.join(output_dir, output_filename)
     # 处理文件已存在的情况
    if os.path.exists(output_path):
        try:
            os.remove(output_path)
        except PermissionError:
            timestamp = int(time.time())
            output_filename = f"{prefix}_{timestamp}_{base_name}"
            output_path = os.path.join(output_dir, output_filename)
    return output_path, output_filename

def save_file(doc, output_path: str):
    """
    保存文件，处理保存失败的情况
    
    Args:
        doc: 文档对象（支持 save 方法）
        output_path: 输出文件路径
    
    Raises:
        Exception: 保存失败时抛出异常
    """
    try:
        doc.save(output_path)
    except Exception as e:
        log.error(f"保存文件失败: {str(e)}")
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
            except:
                pass
        raise