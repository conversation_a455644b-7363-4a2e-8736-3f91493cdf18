from .translateV1 import BaseTranslator
from .openai_translator import OpenAITranslator
from .nllb200_translator import Nllb200Translator
from .utils import is_numeric_or_currency, get_output_path, save_file



def NewTranslator(
        service: str = '',
        llm_api: str = '',
        llm_api_key: str = '',
        llm_model: str = '',
        lang_in: str = '',
        lang_out: str = '',
        thread: int = 1,
        ignore_cache: bool = False,
        prompt: str = '',
    ) -> BaseTranslator:
        translate = None
        for translator in [ OpenAITranslator, Nllb200Translator]:
            if service == translator.name:
                translate = translator(
                    lang_in=lang_in,
                    lang_out=lang_out,
                    llm_model=llm_model,
                    llm_api=llm_api,
                    llm_api_key=llm_api_key,
                    ignore_cache=ignore_cache,
                    thread=thread,
                    prompt=prompt,
                    )
        if not translate:
            raise ValueError("Unsupported translation service")

        return translate
        

# 导出
__all__ = [
    "NewTranslator",
    "is_numeric_or_currency",
    "get_output_path",
    "save_file",
    "BaseTranslator"
]