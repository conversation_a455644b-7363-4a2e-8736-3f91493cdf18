import os
from fastapi import UploadFile
from pathlib import Path
import requests
from typing import <PERSON>ple

async def save_file(file: UploadFile, path: str) -> Tuple[str, str]:
   # 确保 upload 目录存在
    upload_dir = Path("upload")
    upload_dir.mkdir(exist_ok=True)
    
    try:
        if file:
            print("Uploaded files detected, saving...")
            # 生成唯一的文件名
            file_path = os.path.join(upload_dir, file.filename)
            with open(file_path, 'wb') as f:
                # print(f"Writing the file: {file}...")
                f.write(file.file.read())
                path = str(Path(file_path).absolute())

        elif (path.startswith("http://") or path.startswith("https://")):
            print("Online files detected, downloading...")
            try:
                r = requests.get(path, allow_redirects=True)
                if r.status_code == 200:
                    # 从 URL 中提取文件名
                    file_name = Path(path).name
                    file_path = upload_dir / file_name
                    with open(file_path, 'wb') as f:
                        print(f"Writing the file: {file_name}...")
                        f.write(r.content)
                        path = str(file_path.absolute().as_posix()) 
                else:
                    return "", f"下载文件失败，HTTP状态码: {r.status_code}"
            except Exception as e:
                return "", f"下载文件时出错。请检查链接。Error: {e}"
        else:
            # 如果既没有上传文件也没有提供有效URL，但path不为空
            if path:
                # 检查是否为本地文件路径
                local_path = Path(path)
                if local_path.exists() and local_path.is_file():
                    return path, ""
                else:
                    return "", f"提供的路径无效或文件不存在: {path}"
            else:
                return "", "未提供文件或有效的URL"
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return "", f"{e}"

    return path, ""