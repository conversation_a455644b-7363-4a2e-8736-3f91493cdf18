from .pdf_v1 import pdf_translate
import fitz  # PyMuPDF
import os
from ocrmypdf import ocr

def translate_image(
    file: str,
    output: str,
    service: str = '',
    llm_api: str = '',
    llm_api_key: str = '',
    llm_model: str = '',
    lang_in: str = '',
    lang_out: str = '',
    thread: int = 1,
    ignore_cache: bool = False,
    prompt: str = '',
    ocr_lang:str = '',
    is_ocr:bool = False
):
    if not os.path.exists(file):
        raise Exception(f"找不到文件: {file}")

    
    ocrFile = os.path.join(os.path.dirname(file), "ocr_"+os.path.basename(file)+".pdf")
    if ocr_lang == "auto":
        language = None
    else:
        language = ocr_lang
    ocrRes = ocr(file, ocrFile, deskew=True, language=language, force_ocr=True)
    if ocrRes != 0:
        raise Exception(f"OCR识别失败: {str(ocrRes)}")
    result = pdf_translate(
        file=ocrFile,
        output=output,
        service=service,
        llm_api=llm_api,
        llm_api_key=llm_api_key,
        llm_model=llm_model,
        lang_in=lang_in,
        lang_out=lang_out,
        thread=thread,
        ignore_cache=ignore_cache,
        prompt=prompt,
        ocr_lang=ocr_lang,
        is_ocr=False
    )
    outfile = os.path.join(os.path.dirname(file), "translate_"+os.path.basename(file))
    # 打开 PDF
    doc = fitz.open(result["file"])
    page = doc[0]  # 第一页

    # 获取页面中所有文本块的信息
    text_blocks = page.get_text("blocks")  # 每个块是 (x0, y0, x1, y1, text, block_no, block_type)
    
    # 提取所有文本块的 Y 坐标范围
    y_tops = [block[1] for block in text_blocks]
    y_bottoms = [block[3] for block in text_blocks]

    if not y_tops or not y_bottoms:
        print("该页没有检测到文本。")
    else:
        y_min = min(y_tops)
        y_max = max(y_bottoms)

        # 页面整体区域
        rect = page.rect

        # 设定裁剪区域：左上角到文本底部
        crop_rect = fitz.Rect(rect.x0, y_min, rect.x1, y_max)

        # 设置裁剪区域并渲染
        page.set_cropbox(crop_rect)
        pix = page.get_pixmap(dpi=150)
        pix.save(outfile)

    doc.close()
    return {
        "text": result["text"],
        "file": outfile
    }